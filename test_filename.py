import re
from datetime import datetime, timed<PERSON><PERSON>

def test_filename_logic():
    # Test the filename logic
    input_name = "Biloxi 08132025.pdf"
    print(f"Input filename: '{input_name}'")
    
    if input_name.startswith('Biloxi'):
        print("Filename starts with 'Biloxi' - processing...")
        # Extract date part and increment by 1 day, change Biloxi to Bilxy
        date_match = re.search(r'(\d{8})', input_name)
        if date_match:
            print(f"Date match found: {date_match.group(1)}")
            date_str = date_match.group(1)
            # Parse date and add 1 day
            try:
                date_obj = datetime.strptime(date_str, '%m%d%Y')
                print(f"Parsed date: {date_obj}")
                next_date = date_obj + timedelta(days=1)
                print(f"Next date: {next_date}")
                new_date_str = next_date.strftime('%m%d%Y')
                print(f"New date string: {new_date_str}")
                output_filename = f"Bilxy {new_date_str}.xlsx"
                print(f"Generated filename: '{output_filename}'")
            except Exception as e:
                print(f"Date parsing error: {e}")
                output_filename = "Bilxy_output.xlsx"
        else:
            print("No date found in filename")
            output_filename = "Bilxy_output.xlsx"
    else:
        print("Filename does not start with 'Biloxi'")
        # Default naming for other files
        base_name = input_name.rsplit('.', 1)[0]
        output_filename = f"{base_name}_processed.xlsx"
    
    print(f"Final output filename: '{output_filename}'")
    return output_filename

if __name__ == "__main__":
    test_filename_logic()
