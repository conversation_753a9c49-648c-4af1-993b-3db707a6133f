#!/usr/bin/env python3

import pandas as pd

def analyze():
    print("Reading files...")
    df_current = pd.read_excel('test_output_final_v4.xlsx')
    df_expected = pd.read_excel('Bilxy 08142025.xlsx')
    
    print(f"Current records: {len(df_current)}")
    print(f"Expected records: {len(df_expected)}")
    print(f"Missing records: {len(df_expected) - len(df_current)}")
    
    # Insurance distribution comparison
    current_counts = df_current['Insurance'].value_counts()
    expected_counts = df_expected['Insurance'].value_counts()
    
    print("\nInsurance differences (showing only differences):")
    all_insurances = set(current_counts.index) | set(expected_counts.index)
    
    for insurance in sorted(all_insurances):
        current_count = current_counts.get(insurance, 0)
        expected_count = expected_counts.get(insurance, 0)
        diff = expected_count - current_count
        if diff != 0:
            print(f"  {insurance}: Current={current_count}, Expected={expected_count}, Diff={diff}")
    
    # Find completely missing insurance types
    missing_insurances = set(expected_counts.index) - set(current_counts.index)
    if missing_insurances:
        print(f"\nCompletely missing insurance types:")
        for insurance in missing_insurances:
            print(f"  {insurance}: {expected_counts[insurance]} records")

if __name__ == '__main__':
    analyze()
